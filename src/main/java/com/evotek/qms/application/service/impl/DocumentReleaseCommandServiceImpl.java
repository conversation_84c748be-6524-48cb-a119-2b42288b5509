package com.evotek.qms.application.service.impl;

import com.evotek.common.enums.PermissionIdentification;
import com.evotek.common.enums.Scope;
import com.evotek.common.exception.ResponseException;
import com.evotek.common.model.domain.cmd.PolicyResourceScopeCreateCmd;
import com.evotek.common.model.dto.response.iam.UserDTO;
import com.evotek.common.model.dto.response.organization.OrganizationUnitDTO;
import com.evotek.common.service.ShareService;
import com.evotek.common.webapp.support.SecurityUtils;
import com.evotek.qms.application.dto.mapper.DocumentReleaseDTOMapper;
import com.evotek.qms.application.dto.request.DocumentReleaseCreateOrUpdateRequest;
import com.evotek.qms.application.dto.request.ReleaseDocumentRequest;
import com.evotek.qms.application.dto.response.DocumentReleaseDTO;
import com.evotek.qms.application.mapper.QmsCommandMapper;
import com.evotek.qms.application.service.DocumentReleaseCommandService;
import com.evotek.qms.domain.Document;
import com.evotek.qms.domain.DocumentChangeLog;
import com.evotek.qms.domain.DocumentRelease;
import com.evotek.qms.domain.command.DocumentChangeLogCreateOrUpdateCmd;
import com.evotek.qms.domain.command.DocumentRecipientCreateCmd;
import com.evotek.qms.domain.command.DocumentReleaseCreateOrUpdateCmd;
import com.evotek.qms.domain.command.ReleaseDocumentCmd;
import com.evotek.qms.domain.repository.DocumentDomainRepository;
import com.evotek.qms.domain.repository.DocumentReleaseDomainRepository;
import com.evotek.qms.infrastructure.support.enums.DocumentReleaseType;
import com.evotek.qms.infrastructure.support.enums.RecipientDomainType;
import com.evotek.qms.infrastructure.support.exception.BadRequestError;
import com.github.kagkarlsson.scheduler.Scheduler;
import com.github.kagkarlsson.scheduler.task.Task;
import com.github.kagkarlsson.scheduler.task.TaskInstance;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public class DocumentReleaseCommandServiceImpl implements DocumentReleaseCommandService {
    DocumentReleaseDomainRepository documentReleaseDomainRepository;
    DocumentDomainRepository documentDomainRepository;
    QmsCommandMapper qmsCommandMapper;
    DocumentReleaseDTOMapper documentReleaseDTOMapper;
    ShareService shareService;
    Scheduler scheduler;
    Task<UUID> sendDocumentReleaseNofificationTask;


    public DocumentReleaseCommandServiceImpl(DocumentReleaseDomainRepository documentReleaseDomainRepository,
                                             DocumentDomainRepository documentDomainRepository,
                                             QmsCommandMapper qmsCommandMapper,
                                             DocumentReleaseDTOMapper documentReleaseDTOMapper,
                                             ShareService shareService,
                                             Scheduler scheduler,
                                             @Qualifier("notifyDocumentReleaseTask") Task<UUID> sendDocumentReleaseNofificationTask) {
        this.documentReleaseDomainRepository = documentReleaseDomainRepository;
        this.documentDomainRepository = documentDomainRepository;
        this.qmsCommandMapper = qmsCommandMapper;
        this.documentReleaseDTOMapper = documentReleaseDTOMapper;
        this.shareService = shareService;
        this.scheduler = scheduler;
        this.sendDocumentReleaseNofificationTask = sendDocumentReleaseNofificationTask;
    }

    @Override
    @Transactional
    public DocumentReleaseDTO create(DocumentReleaseCreateOrUpdateRequest request) {
        DocumentReleaseCreateOrUpdateCmd documentReleaseCreateOrUpdateCmd = this.qmsCommandMapper.from(request);
        documentReleaseCreateOrUpdateCmd.setWorkerId(SecurityUtils.getWorkerId());
        //Validate Document Version Id
        List<UUID> documentVersionIds = documentReleaseCreateOrUpdateCmd.getDocuments().stream()
                .map(DocumentChangeLogCreateOrUpdateCmd::getDocumentVersionId).collect(Collectors.toCollection(ArrayList::new));
        this.validateDocumentVersionId(documentVersionIds);
        //Save document Release
        DocumentRelease documentRelease = new DocumentRelease(documentReleaseCreateOrUpdateCmd);
        this.documentReleaseDomainRepository.save(documentRelease);
        return this.documentReleaseDTOMapper.domainToDTO(documentRelease);
    }

    @Override
    @Transactional
    public void updateById(UUID id, DocumentReleaseCreateOrUpdateRequest request) {
        DocumentReleaseCreateOrUpdateCmd documentReleaseCreateOrUpdateCmd = this.qmsCommandMapper.from(request);
        //Validate Document Version Id
        List<UUID> documentVersionIds = documentReleaseCreateOrUpdateCmd
                .getDocuments().stream()
                .map(DocumentChangeLogCreateOrUpdateCmd::getDocumentVersionId)
                .collect(Collectors.toCollection(ArrayList::new));
        this.validateDocumentVersionId(documentVersionIds);
        //Save document Release
        DocumentRelease documentRelease = this.documentReleaseDomainRepository.getById(id);
        documentRelease.update(documentReleaseCreateOrUpdateCmd);
        this.documentReleaseDomainRepository.save(documentRelease);
    }

    @Override
    @Transactional
    public void deleteById(UUID id) {
        DocumentRelease releaseNotification = this.documentReleaseDomainRepository.getById(id);
        releaseNotification.delete();
        this.documentReleaseDomainRepository.save(releaseNotification);
    }

    @Override
    @Transactional
    public void release(UUID id, ReleaseDocumentRequest request) {
        DocumentRelease documentRelease = this.documentReleaseDomainRepository.getById(id);
        ReleaseDocumentCmd releaseDocumentCmd = this.qmsCommandMapper.from(request);

        List<DocumentRecipientCreateCmd> recipientCreateCmd = this.validateReleaseRecipient(request);
        releaseDocumentCmd.setRecipients(recipientCreateCmd);

        //Validate domain id
        List<PolicyResourceScopeCreateCmd> scopeCreateCmds = this.validateReleaseTarget(request);
        releaseDocumentCmd.setResourceScopes(scopeCreateCmds);
        //Enrich changeLog
        this.enrichDocumentChangeLog(documentRelease.getDocuments());
        //Save database
        documentRelease.release(releaseDocumentCmd);
        this.documentReleaseDomainRepository.save(documentRelease);
        //Save Document
        List<Document> documents = documentRelease.getDocuments().stream().map(DocumentChangeLog::getDocument).toList();
        this.documentDomainRepository.saveAll(documents);
        //Send Notification
        this.scheduleReleaseDocumentNotification(documentRelease);
    }

    @Override
    @Transactional
    public void updateAndReRelease(UUID id, DocumentReleaseCreateOrUpdateRequest request) {
        this.updateById(id, request);
        DocumentRelease documentRelease = this.documentReleaseDomainRepository.getById(id);
        //Enrich changeLog
        List<DocumentChangeLog> documentChangeLogs = documentRelease.getDocuments();
        this.enrichDocumentChangeLog(documentChangeLogs);
        //Save Document
        List<Document> documents = documentRelease.getDocuments().stream()
                .map(DocumentChangeLog::getDocument)
                .toList();
        this.documentDomainRepository.saveAll(documents);
    }

    private void scheduleReleaseDocumentNotification(DocumentRelease documentRelease) {
        TaskInstance<UUID> issueEventRequestTaskInstance = this.sendDocumentReleaseNofificationTask
                .instance(UUID.randomUUID().toString(), documentRelease.getId());
        this.scheduler.schedule(issueEventRequestTaskInstance, Instant.now());
    }

    private List<DocumentRecipientCreateCmd> validateReleaseRecipient(ReleaseDocumentRequest request) {
        List<UUID> domainIds = request.getDomainIds();
        if (Objects.isNull(domainIds) || domainIds.isEmpty() || request.getReleaseType().equals(DocumentReleaseType.FIRM)) {
//            domainIds = shareService.findAllOrganization();
//            return this.validateOrganizationRecipient(domainIds);
            return List.of();
        }
        if (request.getReleaseType() == DocumentReleaseType.USER) {
            return this.validateUserRecipient(domainIds);
        } else if (request.getReleaseType() == DocumentReleaseType.ORGANIZATION) {
            return this.validateOrganizationRecipient(domainIds);
        }
        throw new ResponseException(BadRequestError.UNSUPPORTED_RELEASE_TYPE);
    }

    private List<PolicyResourceScopeCreateCmd> validateReleaseTarget(ReleaseDocumentRequest request) {
        List<UUID> domainIds = request.getDomainIds();
        if (request.getReleaseType().equals(DocumentReleaseType.FIRM)) {
            domainIds = shareService.findAllOrganization();
            return this.validateOrganizationIds(domainIds);
        } else if (Objects.isNull(domainIds) || domainIds.isEmpty()) {
            throw new ResponseException(BadRequestError.UNSUPPORTED_RELEASE_TYPE);
        }
        if (request.getReleaseType() == DocumentReleaseType.USER) {
            return this.validateUserIds(domainIds);
        } else if (request.getReleaseType() == DocumentReleaseType.ORGANIZATION) {
            return this.validateOrganizationIds(domainIds);
        }
        throw new ResponseException(BadRequestError.UNSUPPORTED_RELEASE_TYPE);
    }

    private List<DocumentRecipientCreateCmd> validateUserRecipient(List<UUID> userIds) {
        List<UserDTO> userDTOs = this.shareService.findByUserIds(userIds);
        if (userDTOs.size() == userIds.size()) {
            return userDTOs.stream().map(userDTO -> {
                DocumentRecipientCreateCmd documentRecipientCreateCmd = new DocumentRecipientCreateCmd();
                documentRecipientCreateCmd.setDomainId(userDTO.getWorkerId());
                documentRecipientCreateCmd.setRecipientDomainType(RecipientDomainType.USER);
                return documentRecipientCreateCmd;
            }).toList();
        }
        throw new ResponseException(BadRequestError.INVALID_RECIPIENT_IDENTITY);
    }

    private List<DocumentRecipientCreateCmd> validateOrganizationRecipient(List<UUID> organizationIds) {
        List<OrganizationUnitDTO> organizationUnitDTOS = this.shareService.findOrganizationUnitByIds(organizationIds);
        if (organizationUnitDTOS.size() == organizationIds.size()) {
            return organizationIds.stream().map(id -> {
                DocumentRecipientCreateCmd documentRecipientCreateCmd = new DocumentRecipientCreateCmd();
                documentRecipientCreateCmd.setDomainId(id);
                documentRecipientCreateCmd.setRecipientDomainType(RecipientDomainType.ORGANIZATION);
                return documentRecipientCreateCmd;
            }).toList();
        }
        throw new ResponseException(BadRequestError.INVALID_RECIPIENT_IDENTITY);
    }

    private List<PolicyResourceScopeCreateCmd> validateUserIds(List<UUID> userIds) {
        List<UserDTO> userDTOs = this.shareService.findByUserIds(userIds);
        if (userDTOs.size() == userIds.size()) {
            return userDTOs.stream().map(userDTO -> PolicyResourceScopeCreateCmd.builder()
                    .piId(userDTO.getWorkerId())
                    .piType(PermissionIdentification.USER)
                    .scope(Scope.VIEW)
                    .build()).toList();
        }
        throw new ResponseException(BadRequestError.INVALID_RECIPIENT_IDENTITY);
    }

    private List<PolicyResourceScopeCreateCmd> validateOrganizationIds(List<UUID> organizationIds) {
        List<OrganizationUnitDTO> organizationUnitDTOS = this.shareService.findOrganizationUnitByIds(organizationIds);
        if (organizationUnitDTOS.size() == organizationIds.size()) {
            return organizationIds.stream().map(id -> PolicyResourceScopeCreateCmd.builder()
                    .piId(id)
                    .piType(PermissionIdentification.ORGANIZATION_UNIT)
                    .scope(Scope.VIEW)
                    .build()).toList();
        }
        throw new ResponseException(BadRequestError.INVALID_RECIPIENT_IDENTITY);
    }

    private void validateDocumentVersionId(List<UUID> documentVersionId) {
        if (Objects.isNull(documentVersionId) || documentVersionId.isEmpty()) {
            throw new ResponseException(BadRequestError.EMPTY_DOCUMENT);
        }
        List<UUID> versionIds = documentVersionId.stream().filter(Objects::nonNull).toList();
        List<Document> documents = this.documentDomainRepository.findAllByDocumentVersionId(versionIds);
        if (documents.size() != versionIds.size()) {
            throw new ResponseException(BadRequestError.DOCUMENT_VERSION_NOT_FOUND);
        }
    }

    private void enrichDocumentChangeLog(List<DocumentChangeLog> documentChangeLogs) {
        List<UUID> documentVersionIds = documentChangeLogs.stream().map(DocumentChangeLog::getDocumentVersionId).toList();
        List<Document> documents = this.documentDomainRepository.findAllByDocumentVersionId(documentVersionIds);
        for (DocumentChangeLog documentChangeLog : documentChangeLogs) {
            Document document = documents
                    .stream()
                    .filter(document_ -> document_.getVersions()
                            .stream()
                            .anyMatch((version_) -> version_.getId().equals(documentChangeLog.getDocumentVersionId())))
                    .findFirst()
                    .orElse(null);
            documentChangeLog.enrichDocument(document);
        }
    }
}
